import { describe, it, expect } from 'vitest';
import { formatPacCode } from '@/utils/helpers';

describe('formatPacCode', () => {
  it('removes spaces and converts to uppercase', () => {
    expect(formatPacCode('pac 123 456 789')).toBe('PAC123456789');
    expect(formatPacCode('pac1 23 456 78 9')).toBe('PAC123456789');
  });

  it('handles already formatted codes', () => {
    expect(formatPacCode('PAC123456789')).toBe('PAC123456789');
  });

  it('handles mixed case input', () => {
    expect(formatPacCode('PaC123456789')).toBe('PAC123456789');
  });

  it('returns original if format is invalid', () => {
    // Too short
    expect(formatPacCode('PAC123')).toBe('PAC123');
    // Wrong prefix
    expect(formatPacCode('ABC123456789')).toBe('ABC123456789');
    // Contains non-alphanumeric
    expect(formatPacCode('PAC-123-456-789')).toBe('PAC-123-456-789');
  });

  it('handles empty string', () => {
    expect(formatPacCode('')).toBe('');
  });
});
