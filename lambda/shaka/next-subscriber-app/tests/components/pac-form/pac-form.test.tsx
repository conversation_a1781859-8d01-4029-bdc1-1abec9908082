import { describe, it, expect, vi, beforeEach } from 'vitest';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { customRender } from '../../utils/custom-render';
import { PACFormData } from '@/src/uswitch/app/signup/hooks/usePACFormSubmission';

// Mock dependencies
const mockPush = vi.fn();
const mockValidateNumberPortingForm = vi.fn();
const mockFormatPacCode = vi.fn();
const mockFlattenValidationErrors = vi.fn();

vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush
  })
}));

vi.mock('@/src/uswitch/schemas/schemas', () => ({
  validateNumberPortingForm: mockValidateNumberPortingForm
}));

vi.mock('@/utils/helpers', () => ({
  formatPacCode: mockFormatPacCode,
  flattenValidationErrors: mockFlattenValidationErrors
}));

vi.mock('@/src/uswitch/routes/route-config', () => ({
  ROUTES_CONFIG: {
    payment: { path: '/payment' }
  }
}));

// Test data factories
const getValidPACFormData = (
  overrides?: Partial<PACFormData>
): PACFormData => {
  return {
    pacCode: 'PAC123456',
    phoneNumber: '07123456789',
    day: '15',
    month: '02',
    year: '2025',
    ...overrides
  };
};

const getInvalidPACFormData = (
  overrides?: Partial<PACFormData>
): PACFormData => {
  return {
    pacCode: 'INVALID',
    phoneNumber: 'invalid-phone',
    day: '',
    month: '',
    year: '',
    ...overrides
  };
};

const getMockValidationSuccess = () => ({
  success: true,
  data: getValidPACFormData()
});

const getMockValidationError = (errors: string[] = ['Invalid PAC code']) => ({
  success: false,
  error: {
    format: () => ({
      pacCode: { _errors: errors }
    })
  }
});

// Import the component after mocks are set up
import { PACForm } from '@/src/uswitch/app/signup/number-porting/page';

// Mock Button component
vi.mock('@/src/uswitch/components/button/button', () => ({
  default: ({ children, form, isLoading, className, ...props }: any) => (
    <button
      type="submit"
      form={form}
      disabled={isLoading}
      className={className}
      {...props}
    >
      {children}
    </button>
  )
}));

// Test wrapper component that includes the form and submit button
function PACFormTestWrapper() {
  return (
    <>
      <PACForm />
      <button type="submit" form="pacForm">
        Keep your number
      </button>
    </>
  );
}

describe('PAC Form', () => {
  const user = userEvent.setup();

  beforeEach(() => {
    vi.clearAllMocks();
    mockFormatPacCode.mockImplementation((code: string) => code.toUpperCase());
    mockFlattenValidationErrors.mockImplementation((errors) =>
      Object.values(errors).flat().filter(Boolean)
    );
  });

  describe('Form Rendering', () => {
    it('should display PAC code input with correct label and placeholder', () => {
      customRender(<PACFormTestWrapper />);

      const pacCodeInput = screen.getByLabelText('Your PAC code');
      expect(pacCodeInput).toBeInTheDocument();
      expect(pacCodeInput).toHaveAttribute('placeholder', 'PAC432564');
      expect(pacCodeInput).toHaveAttribute('type', 'text');
    });

    it('should display phone number input with correct label and placeholder', () => {
      customRender(<PACFormTestWrapper />);

      const phoneInput = screen.getByLabelText('Your phone number');
      expect(phoneInput).toBeInTheDocument();
      expect(phoneInput).toHaveAttribute('placeholder', '07123456789');
      expect(phoneInput).toHaveAttribute('type', 'tel');
      expect(phoneInput).toHaveAttribute('inputMode', 'numeric');
    });

    it('should display optional porting date label', () => {
      customRender(<PACFormTestWrapper />);

      expect(screen.getByText('Choose your porting date (optional)')).toBeInTheDocument();
    });

    it('should not show error alert initially', () => {
      customRender(<PACFormTestWrapper />);

      expect(screen.queryByRole('alert')).not.toBeInTheDocument();
    });
  });

  describe('Form Submission with Valid Data', () => {
    it('should navigate to payment page when form is submitted with valid data', async () => {
      mockValidateNumberPortingForm.mockReturnValue(getMockValidationSuccess());

      customRender(<PACFormTestWrapper />);

      const pacCodeInput = screen.getByLabelText('Your PAC code');
      const phoneInput = screen.getByLabelText('Your phone number');
      const submitButton = screen.getByRole('button', { name: /keep your number/i });

      await user.type(pacCodeInput, 'PAC123456');
      await user.type(phoneInput, '07123456789');
      await user.click(submitButton);

      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith('/payment');
      });
    });

    it('should format PAC code before validation', async () => {
      mockValidateNumberPortingForm.mockReturnValue(getMockValidationSuccess());
      mockFormatPacCode.mockReturnValue('PAC123456');

      customRender(<PACFormTestWrapper />);

      const pacCodeInput = screen.getByLabelText('Your PAC code');
      const phoneInput = screen.getByLabelText('Your phone number');
      const submitButton = screen.getByRole('button', { name: /keep your number/i });

      await user.type(pacCodeInput, 'pac 123 456');
      await user.type(phoneInput, '07123456789');
      await user.click(submitButton);

      expect(mockFormatPacCode).toHaveBeenCalledWith('pac 123 456');
      expect(mockValidateNumberPortingForm).toHaveBeenCalledWith(
        expect.objectContaining({
          pacCode: 'PAC123456'
        })
      );
    });
  });

  describe('Form Validation Errors', () => {
    it('should display validation errors when form submission fails', async () => {
      const errorMessages = ['PAC code must be in the format PAC followed by 6 digits'];
      mockValidateNumberPortingForm.mockReturnValue(getMockValidationError(errorMessages));
      mockFlattenValidationErrors.mockReturnValue(errorMessages);

      customRender(<PACFormTestWrapper />);

      const pacCodeInput = screen.getByLabelText('Your PAC code');
      const phoneInput = screen.getByLabelText('Your phone number');
      const submitButton = screen.getByRole('button', { name: /keep your number/i });

      await user.type(pacCodeInput, 'INVALID');
      await user.type(phoneInput, '07123456789');
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByRole('alert')).toBeInTheDocument();
        expect(screen.getByText('Please fix the following errors:')).toBeInTheDocument();
        expect(screen.getByText(/PAC code must be in the format/)).toBeInTheDocument();
      });
    });

    it('should clear errors when error alert is dismissed', async () => {
      const errorMessages = ['Invalid PAC code'];
      mockValidateNumberPortingForm.mockReturnValue(getMockValidationError(errorMessages));
      mockFlattenValidationErrors.mockReturnValue(errorMessages);

      customRender(<PACFormTestWrapper />);

      const pacCodeInput = screen.getByLabelText('Your PAC code');
      const submitButton = screen.getByRole('button', { name: /keep your number/i });

      await user.type(pacCodeInput, 'INVALID');
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByRole('alert')).toBeInTheDocument();
      });

      const dismissButton = screen.getByLabelText('Dismiss alert');
      await user.click(dismissButton);

      await waitFor(() => {
        expect(screen.queryByRole('alert')).not.toBeInTheDocument();
      });
    });

    it('should not navigate when validation fails', async () => {
      mockValidateNumberPortingForm.mockReturnValue(getMockValidationError());
      mockFlattenValidationErrors.mockReturnValue(['Invalid data']);

      customRender(<PACFormTestWrapper />);

      const pacCodeInput = screen.getByLabelText('Your PAC code');
      const submitButton = screen.getByRole('button', { name: /keep your number/i });

      await user.type(pacCodeInput, 'INVALID');
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByRole('alert')).toBeInTheDocument();
      });

      expect(mockPush).not.toHaveBeenCalled();
    });
  });

  describe('Date Handling', () => {
    it('should submit form without date fields when no date is selected', async () => {
      mockValidateNumberPortingForm.mockReturnValue(getMockValidationSuccess());

      customRender(<PACFormTestWrapper />);

      const pacCodeInput = screen.getByLabelText('Your PAC code');
      const phoneInput = screen.getByLabelText('Your phone number');
      const submitButton = screen.getByRole('button', { name: /keep your number/i });

      await user.type(pacCodeInput, 'PAC123456');
      await user.type(phoneInput, '07123456789');
      await user.click(submitButton);

      expect(mockValidateNumberPortingForm).toHaveBeenCalledWith(
        expect.objectContaining({
          pacCode: 'PAC123456',
          phoneNumber: '07123456789'
        })
      );

      // Should not include day, month, year when not provided
      const callArgs = mockValidateNumberPortingForm.mock.calls[0][0];
      expect(callArgs).not.toHaveProperty('day');
      expect(callArgs).not.toHaveProperty('month');
      expect(callArgs).not.toHaveProperty('year');
    });

    it('should include date fields when date is selected', async () => {
      mockValidateNumberPortingForm.mockReturnValue(getMockValidationSuccess());

      customRender(<PACFormTestWrapper />);

      const pacCodeInput = screen.getByLabelText('Your PAC code');
      const phoneInput = screen.getByLabelText('Your phone number');

      // Simulate date selection through hidden inputs (as the date picker creates these)
      const dayInput = screen.getByDisplayValue('15') || screen.getByRole('combobox', { name: /day/i });
      const monthInput = screen.getByDisplayValue('02') || screen.getByRole('combobox', { name: /month/i });
      const yearInput = screen.getByDisplayValue('2025') || screen.getByRole('combobox', { name: /year/i });

      const submitButton = screen.getByRole('button', { name: /keep your number/i });

      await user.type(pacCodeInput, 'PAC123456');
      await user.type(phoneInput, '07123456789');

      // If date inputs are available, interact with them
      if (dayInput && monthInput && yearInput) {
        await user.selectOptions(dayInput, '15');
        await user.selectOptions(monthInput, '02');
        await user.selectOptions(yearInput, '2025');
      }

      await user.click(submitButton);

      expect(mockValidateNumberPortingForm).toHaveBeenCalledWith(
        expect.objectContaining({
          pacCode: 'PAC123456',
          phoneNumber: '07123456789',
          day: '15',
          month: '02',
          year: '2025'
        })
      );
    });
  });

  describe('Loading States', () => {
    it('should show loading state during form submission', async () => {
      let resolveValidation: (value: any) => void;
      const validationPromise = new Promise((resolve) => {
        resolveValidation = resolve;
      });

      mockValidateNumberPortingForm.mockReturnValue(validationPromise);

      customRender(<PACFormTestWrapper />);

      const pacCodeInput = screen.getByLabelText('Your PAC code');
      const phoneInput = screen.getByLabelText('Your phone number');
      const submitButton = screen.getByRole('button', { name: /keep your number/i });

      await user.type(pacCodeInput, 'PAC123456');
      await user.type(phoneInput, '07123456789');
      await user.click(submitButton);

      // Check if loading state is indicated (button disabled, loading text, etc.)
      expect(submitButton).toBeDisabled();

      // Resolve the validation
      resolveValidation!(getMockValidationSuccess());

      await waitFor(() => {
        expect(submitButton).not.toBeDisabled();
      });
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty form submission gracefully', async () => {
      const errorMessages = ['PAC code is required', 'Phone number is required'];
      mockValidateNumberPortingForm.mockReturnValue(getMockValidationError(errorMessages));
      mockFlattenValidationErrors.mockReturnValue(errorMessages);

      customRender(<PACFormTestWrapper />);

      const submitButton = screen.getByRole('button', { name: /keep your number/i });
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByRole('alert')).toBeInTheDocument();
      });

      expect(mockPush).not.toHaveBeenCalled();
    });

    it('should handle network errors during submission', async () => {
      const networkError = new Error('Network error');
      mockValidateNumberPortingForm.mockImplementation(() => {
        throw networkError;
      });

      customRender(<PACFormTestWrapper />);

      const pacCodeInput = screen.getByLabelText('Your PAC code');
      const phoneInput = screen.getByLabelText('Your phone number');
      const submitButton = screen.getByRole('button', { name: /keep your number/i });

      await user.type(pacCodeInput, 'PAC123456');
      await user.type(phoneInput, '07123456789');
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByRole('alert')).toBeInTheDocument();
      });

      expect(mockPush).not.toHaveBeenCalled();
    });

    it('should preserve form data when validation fails', async () => {
      mockValidateNumberPortingForm.mockReturnValue(getMockValidationError());
      mockFlattenValidationErrors.mockReturnValue(['Invalid data']);

      customRender(<PACFormTestWrapper />);

      const pacCodeInput = screen.getByLabelText('Your PAC code');
      const phoneInput = screen.getByLabelText('Your phone number');
      const submitButton = screen.getByRole('button', { name: /keep your number/i });

      await user.type(pacCodeInput, 'PAC123456');
      await user.type(phoneInput, '07123456789');
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByRole('alert')).toBeInTheDocument();
      });

      // Form should preserve the entered values
      expect(pacCodeInput).toHaveValue('PAC123456');
      expect(phoneInput).toHaveValue('07123456789');
    });
  });
});
