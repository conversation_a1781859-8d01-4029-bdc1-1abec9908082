import { UK_PUBLIC_HOLIDAYS } from '@/utils/constants';

export function getStepFromPath<TRoutes extends Record<string, string>>(
  stepRoutes: TRoutes,
  pathname: string | null
): keyof TRoutes | undefined {
  if (!pathname) return undefined;

  const pathSegments = pathname.split('/').filter(Boolean);
  const lastSegment = pathSegments[pathSegments.length - 1] || '';

  for (const [step, route] of Object.entries(stepRoutes)) {
    if (route === lastSegment) {
      return step as keyof TRoutes;
    }
  }

  return undefined;
}

export const createStepRoutes = (steps: readonly string[]) => {
  return steps.reduce<Record<string, string>>((acc, step) => {
    acc[step] = step;
    return acc;
  }, {});
};

export type ValidationErrorObject = {
  _errors?: string[];
  [key: string]: any;
};

export const flattenValidationErrors = (
  errors: ValidationErrorObject,
  parentKey?: string
): string[] => {
  const result: string[] = [];

  if (errors._errors) {
    const hasErrors =
      Array.isArray(errors._errors) && errors._errors.length > 0;
    if (hasErrors) {
      const prefix = parentKey ? `${parentKey}: ` : '';
      result.push(...errors._errors.map((msg) => `${prefix}${msg}`));
    }
  }

  for (const key of Object.keys(errors)) {
    if (key === '_errors') continue;
    const value = errors[key];
    if (typeof value === 'object' && value !== null) {
      result.push(...flattenValidationErrors(value, key));
    }
  }

  return result;
};

export const isUKPublicHoliday = (date: Date): boolean => {
  const dateString = date.toISOString().split('T')[0];
  return UK_PUBLIC_HOLIDAYS.includes(dateString);
};

export const getAvailableDatesRange = (
  startDate: Date,
  maxDays: number = 14
) => {
  const availableDates: Date[] = [];
  let daysAdded = 0;
  let currentOffset = 0;

  // Keep adding days until we have enough available dates or reach a reasonable limit
  while (daysAdded < maxDays && currentOffset < 30) {
    const checkDate = new Date(startDate);
    checkDate.setDate(startDate.getDate() + currentOffset);

    // Skip weekends and public holidays
    const dayOfWeek = checkDate.getDay();
    if (dayOfWeek !== 0 && dayOfWeek !== 6 && !isUKPublicHoliday(checkDate)) {
      availableDates.push(new Date(checkDate));
      daysAdded++;
    }

    currentOffset++;
  }

  return availableDates;
};

export const isLeapYear = (year) => {
  const fullYear = year < 100 ? 2000 + parseInt(year) : parseInt(year);
  return (fullYear % 4 === 0 && fullYear % 100 !== 0) || fullYear % 400 === 0;
};

export const formatPacCode = (pacCode: string): string => {
  const cleaned = pacCode.replace(/\s+/g, '').toUpperCase();
  if (/^PAC\d{9}$/i.test(cleaned)) {
    return cleaned;
  }
  return pacCode;
};
