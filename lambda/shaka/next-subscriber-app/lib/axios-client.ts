import axios, {
  AxiosError,
  AxiosInstance,
  AxiosResponse,
  InternalAxiosRequestConfig,
  AxiosRequestConfig as AxiosRequestConfigBase
} from 'axios';
import { TokenService } from '../auth/tokens/token-service';
import { getClientConfig } from '../client-config/client-config';
import { API_ENDPOINTS } from '../auth/api/endpoints';

//TODO:
// add logic and tests for forgotten password and password reset

export interface RequestOptions extends AxiosRequestConfigBase {
  skipAuth?: boolean;
}

export interface RefreshTokenResponse {
  accessToken: string;
  refreshToken: string;
  expiresAt: number;
  idToken: string;
}

export type Token = Pick<RefreshTokenResponse, 'accessToken' | 'refreshToken'>;

interface AxiosClientOptions {
  baseURL?: string;
  clientId?: string;
  timeout?: number;
  headers?: Record<string, string>;
}

// Extend the proper type for internal config
interface ExtendedAxiosRequestConfig extends InternalAxiosRequestConfig {
  skipAuth?: boolean;
  _retry?: boolean;
}

export class AxiosClient {
  private readonly client: AxiosInstance;
  private readonly tokenService: TokenService;
  private readonly refreshTokenFn?: () => Promise<void>;
  private refreshPromise?: Promise<void>; // Prevent race conditions

  constructor(
    tokenService: TokenService,
    options: AxiosClientOptions = {},
    refreshTokenFn?: () => Promise<void>
  ) {
    this.tokenService = tokenService;
    this.refreshTokenFn = refreshTokenFn;

    // Use provided options with fallback to global config
    const globalConfig = getClientConfig();

    const baseURL =
      options.baseURL ||
      `${globalConfig.apiBaseUrl}/s/api/v2/${options.clientId || globalConfig.clientId}`;
    // `${globalConfig.apiBaseUrl}/s/api/v2/${options.clientId || globalConfig.clientId}`;

    this.client = axios.create({
      baseURL,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      timeout: options.timeout || 10000
    });

    this.setupInterceptors();
  }

  private setupInterceptors(): void {
    // Request interceptor - only add auth header here
    this.client.interceptors.request.use(
      async (config: ExtendedAxiosRequestConfig) => {
        // Skip auth for public endpoints
        if (config.skipAuth) {
          return config;
        }

        const accessToken = this.tokenService.getAccessToken();
        if (accessToken) {
          config.headers = config.headers || {};
          config.headers.Authorization = `Bearer ${accessToken}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor for token refresh
    this.client.interceptors.response.use(
      (response: AxiosResponse) => response,
      async (error: AxiosError) => {
        const originalRequest = error.config as ExtendedAxiosRequestConfig;

        // If not 401 or already retried, reject immediately
        if (error.response?.status !== 401 || originalRequest._retry) {
          return Promise.reject(error);
        }

        // Skip refresh for auth endpoints to prevent infinite loops
        if (this.isAuthEndpoint(originalRequest.url)) {
          console.warn('Auth endpoint failed, not attempting token refresh');
          this.tokenService.clearTokens();
          return Promise.reject(error);
        }

        // Skip if no refresh function provided
        if (!this.refreshTokenFn) {
          console.warn('No refresh token function provided, clearing tokens');
          this.tokenService.clearTokens();
          return Promise.reject(error);
        }

        originalRequest._retry = true;

        try {
          // Prevent race conditions with multiple simultaneous 401s
          if (!this.refreshPromise) {
            this.refreshPromise = this.refreshTokenFn().finally(() => {
              this.refreshPromise = undefined;
            });
          }

          await this.refreshPromise;

          const accessToken = this.tokenService.getAccessToken();
          if (!accessToken) {
            throw new Error('No access token available after refresh');
          }

          // Update the original request with new token
          originalRequest.headers = originalRequest.headers || {};
          originalRequest.headers.Authorization = `Bearer ${accessToken}`;

          // Retry the original request
          return this.client(originalRequest);
        } catch (refreshError) {
          console.error('Token refresh failed:', refreshError);
          this.tokenService.clearTokens();

          // Create a more informative error
          const refreshErr = new Error(
            'Authentication failed - please log in again'
          );
          refreshErr.cause = refreshError;
          return Promise.reject(refreshErr);
        }
      }
    );
  }

  public async get<T = any>(
    endpoint: string,
    options?: RequestOptions
  ): Promise<T> {
    const config = {
      ...options,
      skipAuth: options?.skipAuth
    };

    const response = await this.client.get<T>(endpoint, config);
    return response.data;
  }

  public async post<T = any, D = any>(
    endpoint: string,
    data?: D,
    options?: RequestOptions
  ): Promise<T> {
    const config = {
      ...options,
      skipAuth: options?.skipAuth
    };

    const response = await this.client.post<T>(endpoint, data, config);
    return response.data;
  }

  public async put<T = any, D = any>(
    endpoint: string,
    data?: D,
    options?: RequestOptions
  ): Promise<T> {
    const config = {
      ...options,
      skipAuth: options?.skipAuth
    };

    const response = await this.client.put<T>(endpoint, data, config);
    return response.data;
  }

  public async delete<T = any>(
    endpoint: string,
    options?: RequestOptions
  ): Promise<T> {
    const config = {
      ...options,
      skipAuth: options?.skipAuth
    };

    const response = await this.client.delete<T>(endpoint, config);
    return response.data;
  }

  private isAuthEndpoint(url?: string): boolean {
    if (!url) return false;
    const authEndpoints = Object.values(API_ENDPOINTS.auth);

    return authEndpoints.some((endpoint) => url.includes(endpoint));
  }
}
