'use client';

import React from 'react';
import Wrapper from '@/src/uswitch/app/signup/_components/wrapper/wrapper';
import {
  ConditionalWrapper,
  DesktopOnly
} from '@/src/uswitch/app/signup/_components/conditional-wrapper/conditional-wrapper';
import { ProgressBar } from '@/components/progress-bar/progress-bar';
import { Divider } from '@/src/uswitch/app/signup/_components/divider/divider';
import { ROUTES_CONFIG } from '@/src/uswitch/routes/route-config';
import CtaButton from '@/src/uswitch/app/signup/_components/cta-button/cta-button';
import { OrderSummary } from '@/src/uswitch/app/signup/_components/order-summary/order-summary';
import {
  CloseIcon,
  PACIcon,
  PlayButtonIcon,
  TextMessageIcon
} from '@/icons/icons';
import { FullDetailsButton } from '@/src/uswitch/app/signup/_components/full-details-button/full-details-button';
import Modal from '@/components/modal/modal';
import Image from 'next/image';
import PACiphone from 'public/images/PAC-iphone.png';
import { initialState } from '@/src/uswitch/app/signup/reducer/reducer';
import { shouldShowProgressBar } from '@/src/uswitch/utils/helpers';
import { usePlanSelection } from '@/src/uswitch/app/signup/hooks/use-plan-selection';
import Button from '@/src/uswitch/components/button/button';
import { FormAlert } from '@/components/dismissable-alert/dismissable-alert';
import { PacDeliveryDatePicker } from '@/src/uswitch/app/signup/_components/pac-delivery-date-picker/pac-delivery-date-picker';
import {
  PACFormData,
  usePACFormSubmission
} from '@/src/uswitch/app/signup/hooks/usePACFormSubmission';
import { usePathname } from 'next/navigation';

export default function NumberPorting() {
  const pathname = usePathname();
  const showProgressBar = shouldShowProgressBar(pathname);
  const planSelectionState = usePlanSelection(initialState);

  return (
    <>
      <Wrapper>
        <div className="order-2 flex flex-col gap-4 lg:order-1">
          <ConditionalWrapper className="p-6">
            {showProgressBar && (
              <DesktopOnly>
                <ProgressBar withLabel ariaLabel="signup progress bar">
                  <ProgressBar.Track>
                    <ProgressBar.Bar className="bg-blueberry mb-6" />
                  </ProgressBar.Track>
                </ProgressBar>
              </DesktopOnly>
            )}
            <h1 className="mb-4 lg:mb-0">Want to keep an existing number?</h1>
            <Divider className="my-6 hidden lg:block" />
            <h2 className="mt-4 mb-2">How do I keep my number?</h2>
            <p>
              This is really easy to do and only takes a few minutes. You can
              also do this later so don’t worry if you need to skip this for
              now.
            </p>
            <PACInfoGrid />
            <Divider className="mt-8 mb-6" />
            <h2 className="mb-2">Enter your PAC code</h2>
            <p>This is really easy to do and only takes a few minutes.</p>
            <PACForm />
            <Button
              isLoading={false}
              form="pacForm"
              className="bg-primary hover:bg-primary-hover text-bold mt-6 hidden w-full cursor-pointer rounded-[2px] p-3 text-center font-semibold text-white lg:inline-block"
            >
              Keep your number
            </Button>
            <CtaButton
              className="bg-secondary text-primary border-primary hover:bg-secondary-hover mt-4 hidden border hover:text-white lg:inline-block"
              text="Skip for now"
              href={ROUTES_CONFIG['payment'].path}
            />
          </ConditionalWrapper>
        </div>
        <OrderSummary state={planSelectionState} />
      </Wrapper>
      <div className="px-6">
        <Button
          isLoading={false}
          form="pacForm"
          className="bg-primary hover:bg-primary-hover text-bold mt-6 inline-block w-full cursor-pointer rounded-[2px] p-3 text-center font-semibold text-white lg:hidden"
        >
          Keep your number
        </Button>
        <CtaButton
          text="Skip for now"
          href={ROUTES_CONFIG['payment'].path}
          className="bg-secondary text-primary border-primary hover:bg-secondary-hover mt-4 inline-block border hover:text-white lg:hidden"
        />
      </div>
    </>
  );
}

export function PACInfoGrid() {
  return (
    <>
      <div className="mt-6 mb-4 grid gap-4 lg:grid-cols-2">
        <PACCard
          icon={<PACIcon />}
          title="Text “PAC” to 65075"
          description="This notifies your current network you would like to switch away from them and keep your number."
        />
        <div className="order-4 lg:order-none lg:row-span-2">
          <Image
            priority
            className="w-full lg:ml-auto lg:max-w-[340px]"
            src={PACiphone}
            alt="Number porting"
            width={293}
            height={227}
          />
        </div>
        <PACCard
          icon={<TextMessageIcon />}
          title="Enter your code below"
          description="You’ll receive your PAC code from your old network in a few minutes which you need to enter below."
        />
        <div className="order-3 lg:order-none">
          <PlayButtonIcon className="mr-2 inline align-middle" />
          <FullDetailsButton text="Watch an instructional video">
            {({ isOpen, setIsOpen }) =>
              isOpen && (
                <Modal onOpenChange={setIsOpen} open={isOpen}>
                  <Modal.Overlay />
                  <Modal.Content className="w-full rounded-lg p-6 lg:max-w-2xl">
                    <div className="mb-4 flex justify-end">
                      <Modal.Close>
                        <CloseIcon />
                      </Modal.Close>
                    </div>
                    <Modal.Title className="mb-6 text-xl font-semibold">
                      <p>hi</p>
                    </Modal.Title>
                    <Modal.Description>
                      <p>hello</p>
                    </Modal.Description>
                  </Modal.Content>
                </Modal>
              )
            }
          </FullDetailsButton>
        </div>
      </div>
    </>
  );
}

interface PACProps {
  icon: React.ReactNode;
  title: string;
  description: string;
}

function PACCard({ icon, title, description }: PACProps) {
  return (
    <div className="order-1 grid grid-cols-[minmax(0,0.1fr)_minmax(0,0.9fr)] gap-6 lg:order-none">
      {icon}
      <div>
        <h3 className="mb-2 text-[18px]">{title}</h3>
        <p className="text-balanced">{description}</p>
      </div>
    </div>
  );
}

export function PACForm() {
  const { handleSubmit, isLoading, errors, setErrors, formData } =
    usePACFormSubmission();

  console.log(errors);

  const onSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    const form = e.currentTarget;
    const formData = new FormData(form);

    const data: PACFormData = {
      pacCode: formData.get('pacCode') as string,
      phoneNumber: formData.get('phoneNumber') as string,
      day: formData.get('day') as string | undefined,
      month: formData.get('month') as string | undefined,
      year: formData.get('year') as string | undefined
    };

    if (!data.day) {
      delete data.day;
      delete data.month;
      delete data.year;
    }

    handleSubmit(data);
  };

  const hasErrors = Object.keys(errors).length > 0;

  return (
    <>
      <form onSubmit={onSubmit} id="pacForm" className="date-picker mt-4">
        <div>
          <label htmlFor="pacCode" className="font-semibold">
            Your PAC code
          </label>
          <input
            id="pacCode"
            name="pacCode"
            type="text"
            placeholder="PAC432564"
            className="placeholder-placeholder border-border placeholder:text-default mt-2 w-full rounded-[2px] border p-3"
            defaultValue={formData.pacCode}
          />
        </div>
        <div>
          <label htmlFor="phoneNumber" className="font-semibold">
            Your phone number
          </label>
          <input
            id="phoneNumber"
            name="phoneNumber"
            type="tel"
            inputMode="numeric"
            placeholder="07123456789"
            className="placeholder-placeholder border-border placeholder:text-default mt-2 w-full rounded-[2px] border p-3"
            defaultValue={formData.phoneNumber}
          />
        </div>
        <div className="col-span-2 xl:col-span-1">
          <label htmlFor="switchingDate" className="font-semibold">
            {/*- what is the default if skipped - in terms of API call ?*/}
            Choose your porting date (optional)
          </label>
          <PacDeliveryDatePicker />
        </div>
      </form>
      {hasErrors && (
        <FormAlert
          type="error"
          messages={errors}
          title="Please fix the following errors:"
          dismissible={true}
          onDismiss={() => setErrors([])}
          className="mt-4"
        />
      )}
    </>
  );
}
