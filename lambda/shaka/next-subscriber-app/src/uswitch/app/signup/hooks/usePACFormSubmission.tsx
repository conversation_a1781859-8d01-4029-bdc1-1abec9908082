import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { validateNumberPortingForm } from '@/src/uswitch/schemas/schemas';
import { ROUTES_CONFIG } from '@/src/uswitch/routes/route-config';
import { flattenValidationErrors, formatPacCode } from '@/utils/helpers';
import { AxiosError } from 'axios';

export interface PACFormData {
  pacCode: string;
  phoneNumber: string;
  day?: string;
  month?: string;
  year?: string;
}

export function usePACFormSubmission() {
  const router = useRouter();
  // replace with react query
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<string[]>([]);
  const [formData, setFormData] = useState<PACFormData>({
    pacCode: '',
    phoneNumber: '',
    day: '',
    month: '',
    year: ''
  });

  const handleSubmit = async (formData: PACFormData) => {
    setIsLoading(true);
    setErrors([]);

    try {
      const formattedData = {
        ...formData,
        pacCode: formatPacCode(formData.pacCode)
      };

      setFormData(formData);
      const result = validateNumberPortingForm(formattedData);

      if (result.success) {
        router.push(ROUTES_CONFIG['payment'].path);
      } else {
        const formattedErrors = result.error?.format() || {};
        const flatErrors = flattenValidationErrors(formattedErrors);
        setErrors(flatErrors);
      }
    } catch (error: unknown) {
      if (error instanceof AxiosError) {
        console.error('Form submission error:', error);
        setErrors([error.message]);
      }
    } finally {
      setIsLoading(false);
    }
  };

  return { handleSubmit, isLoading, errors, setErrors, formData };
}
