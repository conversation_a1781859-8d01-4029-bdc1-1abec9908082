'use client';

import React, { useEffect, useReducer } from 'react';
import { PlainCard } from '@/components/plain-card/plain-card';
import Wrapper from '@/src/uswitch/app/signup/_components/wrapper/wrapper';
import { OrderSummary } from '@/src/uswitch/app/signup/_components/order-summary/order-summary';
import { ProgressBar } from '@/components/progress-bar/progress-bar';
import { usePathname, useSearchParams, useRouter } from 'next/navigation';
import { ROUTES_CONFIG } from '@/src/uswitch/routes/route-config';
import { Divider } from '@/src/uswitch/app/signup/_components/divider/divider';
import { NetworkInfo } from '@/src/uswitch/app/signup/_components/network-info/network-info';
import { PlanCard } from '@/src/uswitch/app/signup/_components/data-plan-cards/plan-card';
import { DataPlanDetails } from '@/src/uswitch/app/signup/_components/data-plan-cards/data-plans-details';
import CtaButton from '@/src/uswitch/app/signup/_components/cta-button/cta-button';
import {
  ConditionalWrapper,
  DesktopOnly
} from '@/src/uswitch/app/signup/_components/conditional-wrapper/conditional-wrapper';
import { DataPlanCard } from '../_components/data-plan-cards/data-plan-card';
import { DataPlanCardWithFooter } from '@/src/uswitch/app/signup/_components/data-plan-cards/data-plan-with-footer';
import {
  basicPlanCardData,
  globalRoamingPackages,
  familyPlanCardData,
  planOrderItems,
  paramsOptions
} from '@/src/uswitch/utils/constants';
import {
  getInitialStateFromUrl,
  createOrderSummary,
  shouldShowProgressBar
} from '@/src/uswitch/utils/helpers';
import { ACTIONS, initialState, planReducer } from '../reducer/reducer';
import { SelectableCard } from '@/components/selectable-card/selectable-card';

// TODO: heading hierarchy !

export default function PlanSelection() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();
  const showProgressBar = shouldShowProgressBar(pathname);

  const [planSelectionState, dispatchPlanSelectionAction] = useReducer(
    planReducer,
    getInitialStateFromUrl(searchParams, initialState)
  );
  const { basicPlanQuantity, bigFamilyPlanQuantity, selectedRoamingPackageId } =
    planSelectionState;

  useEffect(() => {
    const params = new URLSearchParams(searchParams.toString());
    params.set(paramsOptions.basic, basicPlanQuantity.toString());
    params.set(paramsOptions.family, bigFamilyPlanQuantity.toString());
    params.set(paramsOptions.roaming, selectedRoamingPackageId.toString());

    const planSelectionUrl = `${pathname}?${params.toString()}`;
    router.push(planSelectionUrl, { scroll: false });
  }, [
    basicPlanQuantity,
    bigFamilyPlanQuantity,
    selectedRoamingPackageId,
    pathname,
    router,
    searchParams
  ]);
  const handleBigFamilyPlanIncrement = () => {
    dispatchPlanSelectionAction({ type: ACTIONS.INCREMENT_FAMILY_PLAN });
  };

  const handleBigFamilyPlanDecrement = () => {
    dispatchPlanSelectionAction({ type: ACTIONS.DECREMENT_FAMILY_PLAN });
  };

  const handleBasicPlanIncrement = () => {
    dispatchPlanSelectionAction({ type: ACTIONS.INCREMENT_BASIC_PLAN });
  };

  const handleBasicPlanDecrement = () => {
    dispatchPlanSelectionAction({ type: ACTIONS.DECREMENT_BASIC_PLAN });
  };

  const setSelectedRoamingPackageId = (id: number) => {
    dispatchPlanSelectionAction({
      type: ACTIONS.SET_SELECTED_ROAMING_PACKAGE,
      payload: id
    });
  };

  return (
    <>
      <Wrapper>
        <div className="order-2 flex flex-col gap-4 lg:order-1">
          <ConditionalWrapper className="p-6">
            {showProgressBar && (
              <DesktopOnly>
                <ProgressBar withLabel ariaLabel="signup progress bar">
                  <ProgressBar.Track>
                    <ProgressBar.Bar className="bg-blueberry mb-6" />
                  </ProgressBar.Track>
                </ProgressBar>
              </DesktopOnly>
            )}
            <h1 className="mb-4 lg:mb-0">Plan selection</h1>
            <Divider className="my-6 hidden lg:block" />
            <PlainCard className="p-0">
              <NetworkInfo />
              <PlanCard
                planDetails={planOrderItems[0]}
                plans={
                  <>
                    <DataPlanCard planDetails={basicPlanCardData.plan} />
                    <DataPlanCardWithFooter
                      planDetails={basicPlanCardData.pricing}
                    />
                  </>
                }
                minQuantity={1}
                quantity={basicPlanQuantity}
                handleIncrement={handleBasicPlanIncrement}
                handleDecrement={handleBasicPlanDecrement}
              >
                <DataPlanDetails>
                  <h3 className="mb-2 text-[18px] lg:mb-0">20GB in Europe</h3>
                  <div className="border-border hidden border opacity-20 lg:block" />
                  <h3 className="text-[18px]">
                    Unlimited UK calls, texts and data
                  </h3>
                </DataPlanDetails>
              </PlanCard>
            </PlainCard>
            <h2 className="mt-8 mb-2">Global roaming</h2>
            <p className="mb-6">Add monthly travel data now to save 30%</p>
            <div className="grid grid-cols-2 gap-2 lg:w-fit lg:grid-cols-[auto_auto_auto_auto_auto]">
              {globalRoamingPackages.map((packageData) => (
                <SelectableCard
                  key={packageData.id}
                  itemId={packageData.id}
                  currentSelectedId={selectedRoamingPackageId}
                  value={packageData.data}
                  price={packageData.price}
                  onSelectionChange={setSelectedRoamingPackageId}
                />
              ))}
            </div>
            <Divider className="mb-6" />
            <h2 className="my-2">Big family ?</h2>
            <p className="mb-6">
              Add up to 5 special rate plans for your family members
            </p>
            {/* TODO: Replace with compound component*/}
            <PlainCard className="p-0">
              <NetworkInfo />
              <PlanCard
                planDetails={planOrderItems[1]}
                plans={
                  <>
                    <DataPlanCard planDetails={familyPlanCardData.plan} />
                    <DataPlanCardWithFooter
                      planDetails={familyPlanCardData.pricing}
                    />
                  </>
                }
                minQuantity={0}
                quantity={bigFamilyPlanQuantity}
                handleIncrement={handleBigFamilyPlanIncrement}
                handleDecrement={handleBigFamilyPlanDecrement}
              >
                <DataPlanDetails>
                  <h3 className="mb-2 text-[18px] lg:mb-0">10GB in Europe</h3>
                  <div className="border-border hidden border opacity-20 lg:block" />
                  <h3 className="text-[18px]">
                    Unlimited UK calls, texts and data
                  </h3>
                </DataPlanDetails>
              </PlanCard>
            </PlainCard>
            <Divider />
            <TotalCost
              amount={createOrderSummary(planSelectionState).totalCost}
            />
            <CtaButton
              className="bg-primary hover:bg-primary-hover text-bold mt-6 hidden w-full cursor-pointer rounded-[2px] p-3 text-center font-semibold text-white lg:inline-block"
              text="Go to billing"
              href={{
                pathname: ROUTES_CONFIG['number-porting'].path,
                query: {
                  basic: planSelectionState.basicPlanQuantity.toString(),
                  family: planSelectionState.bigFamilyPlanQuantity.toString(),
                  roaming:
                    planSelectionState.selectedRoamingPackageId?.toString() ??
                    ''
                }
              }}
            />
          </ConditionalWrapper>
        </div>
        <OrderSummary state={planSelectionState} />
      </Wrapper>
      <div className="px-6">
        <CtaButton
          className="bg-primary hover:bg-primary-hover text-bold mt-6 inline-block w-full cursor-pointer rounded-[2px] p-3 text-center font-semibold text-white lg:hidden"
          text="Go to billing"
          href={ROUTES_CONFIG['number-porting'].path}
        />
      </div>
    </>
  );
}

interface TotalCostProps {
  amount: number;
}

function TotalCost({ amount }: TotalCostProps) {
  return (
    <section
      aria-label="Order total"
      className="mt-6 flex items-end justify-between"
    >
      <dl className="flex w-full flex-col items-end">
        <div className="flex w-full items-end justify-between">
          <dt className="text-xs font-bold lg:text-base">Total cost</dt>
          <dd className="text-xs font-bold lg:text-base">
            <strong>£{amount} </strong>{' '}
            <span className="text-default text-xs font-normal lg:text-[18px]">
              a month
            </span>
          </dd>
        </div>
        <span className="text-xxxs text-right">(including VAT)</span>
      </dl>
    </section>
  );
}
