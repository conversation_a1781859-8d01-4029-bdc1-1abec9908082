import { getAvailableDatesRange } from '@/utils/helpers';
import React, { useEffect, useMemo, useState } from 'react';
import { monthNames } from '@/utils/constants';
import { ChevronDown } from '@/icons/icons';

interface DatePickerProps {
  initialState?: { day: string; month: string; year: string };
  disabled?: boolean;
  required?: boolean;
  error?: boolean;
  className?: string;
}

const baseSelectClasses = `
    w-full cursor-pointer appearance-none rounded-[2px] border p-2 xl:p-3
    bg-white text-placeholder rounded-[2px] border border-border
  `;

const iconClasses = `
    pointer-events-none absolute top-1/2 right-3 h-5 w-5 -translate-y-1/2 transform
    transition-colors duration-200
  `;

export function PacDeliveryDatePicker({
  initialState = { day: '', month: '', year: '' },
  error = false,
  className = ''
}: Omit<DatePickerProps, 'disabled' | 'required'>) {
  const today = new Date();
  const currentYear = today.getFullYear();
  const currentMonth = today.getMonth() + 1;

  // Create a new date object for tomorrow to avoid mutating the original date
  const tomorrow = new Date(today);
  tomorrow.setDate(today.getDate() + 1);

  // Format the default date values
  const defaultDay = tomorrow.getDate().toString().padStart(2, '0');
  const defaultMonth = (tomorrow.getMonth() + 1).toString().padStart(2, '0');
  const defaultYear = tomorrow.getFullYear().toString();

  const availableDates = getAvailableDatesRange(tomorrow, 30);

  const datesByMonth = useMemo(() => {
    const grouped: { [key: string]: Date[] } = {};

    availableDates.forEach((date) => {
      const monthKey = (date.getMonth() + 1).toString().padStart(2, '0');
      if (!grouped[monthKey]) {
        grouped[monthKey] = [];
      }
      grouped[monthKey].push(date);
    });

    return grouped;
  }, [availableDates]);

  const availableMonths = Object.keys(datesByMonth).map((monthValue) => ({
    name: monthNames[parseInt(monthValue) - 1],
    value: monthValue,
    disabled: false
  }));

  const [selectedDate, setSelectedDate] = useState({
    day: initialState.day || defaultDay,
    month: initialState.month || defaultMonth,
    year: initialState.year || defaultYear,
  });

  const availableDays = useMemo(() => {
    if (!selectedDate.month || !datesByMonth[selectedDate.month]) {
      return [];
    }

    return datesByMonth[selectedDate.month]
      .map((date) => date.getDate().toString().padStart(2, '0'))
      .sort((a, b) => parseInt(a) - parseInt(b));
  }, [selectedDate.month, datesByMonth]);

  useEffect(() => {
    if (selectedDate.day && !availableDays.includes(selectedDate.day)) {
      setSelectedDate((prev) => ({
        ...prev,
        day: ''
      }));
    }
  }, [availableDays, selectedDate.day]);

  useEffect(() => {
    if (!selectedDate.month && defaultMonth) {
      setSelectedDate((prev) => ({
        ...prev,
        month: defaultMonth
      }));
    }
  }, [defaultMonth, selectedDate.month]);

  return (
    <div
      className={`mt-2 ${className}`}
      role="group"
      aria-labelledby="date-picker-label"
    >
      <div className="grid grid-cols-3 gap-3">
        {/* Day */}
        <div className="relative">
          <label htmlFor="day-select" className="sr-only">
            Day
          </label>
          <select
            id="day-select"
            name="day"
            value={selectedDate.day}
            onChange={(e) =>
              setSelectedDate({ ...selectedDate, day: e.target.value })
            }
            disabled={!selectedDate.month || availableDays.length === 0}
            aria-label="Select day"
            aria-describedby={error ? 'date-error' : undefined}
            className={baseSelectClasses}
          >
            <option value="" disabled>
              DD
            </option>
            {availableDays.map((day) => (
              <option key={day} value={day}>
                {parseInt(day, 10)}
              </option>
            ))}
          </select>
          <ChevronDown className={iconClasses} aria-hidden="true" />
        </div>

        {/* Month */}
        <div className="relative">
          <label htmlFor="month-select" className="sr-only">
            Month
          </label>
          <select
            id="month-select"
            name="month"
            value={selectedDate.month}
            onChange={(e) =>
              setSelectedDate({
                ...selectedDate,
                month: e.target.value,
                day: '' // Reset day when month changes
              })
            }
            aria-label="Select month"
            aria-describedby={error ? 'date-error' : undefined}
            className={baseSelectClasses}
            disabled={availableMonths.length === 0}
          >
            <option value="" disabled>
              {availableMonths.length === 0 ? 'No dates available' : 'MM'}
            </option>
            {availableMonths.map((month) => (
              <option
                key={month.value}
                value={month.value}
                disabled={month.disabled}
              >
                {month.name}
              </option>
            ))}
          </select>
          <ChevronDown className={iconClasses} aria-hidden="true" />
        </div>

        {/* Year - Only current year */}
        <div className="relative">
          <label htmlFor="year-select" className="sr-only">
            Year
          </label>
          <select
            id="year-select"
            name="year"
            defaultValue={currentYear}
            className={baseSelectClasses}
          >
            <option value={currentYear}>{currentYear}</option>
          </select>
          {/*<ChevronDown className={iconClasses} aria-hidden="true" />*/}
        </div>
      </div>

      {error && (
        <div id="date-error" className="mt-1 text-sm text-red-600" role="alert">
          Please select a valid date
        </div>
      )}

      {availableDates.length === 0 && (
        <div className="mt-2 text-sm text-gray-600">
          No available dates within the next 14 business days.
        </div>
      )}
    </div>
  );
}
