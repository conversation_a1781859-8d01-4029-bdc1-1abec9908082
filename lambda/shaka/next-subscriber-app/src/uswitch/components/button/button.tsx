import React from 'react';
import { LoadingSpinner } from '@/icons/icons';

interface ButtonProps extends React.PropsWithChildren {
  className?: string;
  form?: string;
  isLoading?: boolean;
  disabled?: boolean;
}

function Button({
  children,
  className,
  isLoading,
  disabled,
  ...props
}: ButtonProps) {
  return (
    <button
      form="pacForm"
      className={`bg-primary hover:bg-primary-hover text-bold mt-6 hidden w-full cursor-pointer rounded-[2px] p-3 text-center font-semibold text-white disabled:opacity-50 ${className || ''}`}
      disabled={disabled}
      {...props}
    >
      <div className="flex items-center justify-center gap-2">
        {isLoading && <LoadingSpinner />}
        {children}
      </div>
    </button>
  );
}

export default Button;
